/*
 * A9MPCore 全局定时器模块测试用例实现
 *
 * 测试用例范围：UC-KN-3-1 ~ UC-KN-3-6
 */

#include "uc-kn-3.h"

#include "types.h"
#include "bsp_print.h"  /* 串口输出接口 */

/* A9MPCore 全局定时器寄存器定义 */
#define PERIPHBASE                      0x3FFF0000
#define GLOBAL_TIMER_BASE               (PERIPHBASE + 0x200)
#define GLOBAL_TIMER_COUNTER_LOW        (GLOBAL_TIMER_BASE + 0x00)
#define GLOBAL_TIMER_COUNTER_HIGH       (GLOBAL_TIMER_BASE + 0x04)
#define GLOBAL_TIMER_CTLR               (GLOBAL_TIMER_BASE + 0x08)
#define GLOBAL_TIMER_ISR                (GLOBAL_TIMER_BASE + 0x0C)
#define GLOBAL_TIMER_CMP_LOW            (GLOBAL_TIMER_BASE + 0x10)
#define GLOBAL_TIMER_CMP_HIGH           (GLOBAL_TIMER_BASE + 0x14)
#define GLOBAL_TIMER_AUTO_INCR          (GLOBAL_TIMER_BASE + 0x18)

/* 全局定时器寄存器复位值 */
#define GLOBAL_TIMER_COUNTER_LOW_RESET      0x00000000U
#define GLOBAL_TIMER_COUNTER_HIGH_RESET     0x00000000U
#define GLOBAL_TIMER_CTLR_RESET             0x00000000U
#define GLOBAL_TIMER_ISR_RESET              0x00000000U
#define GLOBAL_TIMER_CMP_LOW_RESET          0x00000000U
#define GLOBAL_TIMER_CMP_HIGH_RESET         0x00000000U
#define GLOBAL_TIMER_AUTO_INCR_RESET        0x00000000U

/* 测试用值 */
#define TEST_COUNTER_LOW_VAL                0x12345678U
#define TEST_COUNTER_HIGH_VAL               0x9ABCDEF0U
#define TEST_CTLR_VAL                       0x0000000FU
#define TEST_CMP_LOW_VAL                    0x00100000U
#define TEST_CMP_HIGH_VAL                   0x00000000U
#define TEST_AUTO_INCR_VAL                  0x00050000U

/* 控制寄存器位定义 */
#define GTIMER_ENABLE_BIT                   (1 << 0)
#define GTIMER_COMP_ENABLE_BIT              (1 << 1)
#define GTIMER_IRQ_ENABLE_BIT               (1 << 2)
#define GTIMER_AUTO_INCR_BIT                (1 << 3)

/* 中断状态寄存器位定义 */
#define GTIMER_EVENT_FLAG_BIT               (1 << 0)

/* 全局定时器读取函数（处理64位读取的原子性） */
static uint64_t global_timer_read(void) {
    uint32_t high1, low, high2;

    do {
        high1 = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
        low   = READ_32(GLOBAL_TIMER_COUNTER_LOW);
        high2 = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
    } while (high1 != high2);

    return ((uint64_t)high1 << 32) | low;
}

/* 简单延时函数 */
static void simple_delay(uint32_t count) {
    volatile uint32_t i;
    for (i = 0; i < count; i++) {
        /* 空循环 */
    }
}

void uc_kn_3_1(void)
{
    print2("[INFO] uc_kn_3_1: Starting global timer registers test\n");

    uint32_t val;

    val = READ_32(GLOBAL_TIMER_COUNTER_LOW);
    if (val != GLOBAL_TIMER_COUNTER_LOW_RESET) {
        print2("[FAILED] COUNTER_LOW reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_COUNTER_LOW_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
    if (val != GLOBAL_TIMER_COUNTER_HIGH_RESET) {
        print2("[FAILED] COUNTER_HIGH reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_COUNTER_HIGH_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_CTLR);
    if (val != GLOBAL_TIMER_CTLR_RESET) {
        print2("[FAILED] CTLR reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_CTLR_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_ISR);
    if (val != GLOBAL_TIMER_ISR_RESET) {
        print2("[FAILED] ISR reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_ISR_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_CMP_LOW);
    if (val != GLOBAL_TIMER_CMP_LOW_RESET) {
        print2("[FAILED] CMP_LOW reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_CMP_LOW_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_CMP_HIGH);
    if (val != GLOBAL_TIMER_CMP_HIGH_RESET) {
        print2("[FAILED] CMP_HIGH reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_CMP_HIGH_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_AUTO_INCR);
    if (val != GLOBAL_TIMER_AUTO_INCR_RESET) {
        print2("[FAILED] AUTO_INCR reset mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_AUTO_INCR_RESET);
        return;
    }

    /* 验证不可读寄存器是否通过QEMU日志反馈读取无效 */
    print2("[INFO] Checking QEMU log for invalid-read entries on write-only registers\n");

    /* 向所有寄存器写入合法值 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, TEST_COUNTER_LOW_VAL);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, TEST_COUNTER_HIGH_VAL);
    WRITE_32(GLOBAL_TIMER_CTLR, TEST_CTLR_VAL);
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);  /* 写1清除中断状态 */
    WRITE_32(GLOBAL_TIMER_CMP_LOW, TEST_CMP_LOW_VAL);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, TEST_CMP_HIGH_VAL);
    WRITE_32(GLOBAL_TIMER_AUTO_INCR, TEST_AUTO_INCR_VAL);

    /* 写入后再次读取，确认可写可读寄存器值为刚才写入值 */

    val = READ_32(GLOBAL_TIMER_CTLR);
    if (val != TEST_CTLR_VAL) {
        print2("[FAILED] CTLR RW mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_CTLR_VAL);
        return;
    }

    val = READ_32(GLOBAL_TIMER_ISR);
    if (val != GLOBAL_TIMER_ISR_RESET) {  /* 写1清除后应为0 */
        print2("[FAILED] ISR RW mismatch: read=0x%x, exp=0x%x\n",
               val, GLOBAL_TIMER_ISR_RESET);
        return;
    }

    val = READ_32(GLOBAL_TIMER_CMP_LOW);
    if (val != TEST_CMP_LOW_VAL) {
        print2("[FAILED] CMP_LOW RW mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_CMP_LOW_VAL);
        return;
    }

    val = READ_32(GLOBAL_TIMER_CMP_HIGH);
    if (val != TEST_CMP_HIGH_VAL) {
        print2("[FAILED] CMP_HIGH RW mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_CMP_HIGH_VAL);
        return;
    }

    val = READ_32(GLOBAL_TIMER_AUTO_INCR);
    if (val != TEST_AUTO_INCR_VAL) {
        print2("[FAILED] AUTO_INCR RW mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_AUTO_INCR_VAL);
        return;
    }

    print2("[PASSED] uc_kn_3_1: Global timer registers test completed successfully\n");
}

void uc_kn_3_2(void)
{
    print2("[INFO] uc_kn_3_2: Starting global timer counter test\n");

    uint32_t low_val, high_val;
    uint32_t test_low = 0x12345678;
    uint32_t test_high = 0x9ABCDEF0;

    /* 清除全局定时器控制寄存器的定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    /* 写入低32位定时器计数器寄存器 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, test_low);

    /* 写入高32位定时器计数器寄存器 */
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, test_high);

    /* 读取定时器高32位计数器寄存器和低32位计数器寄存器的值，确认和写入值一致 */
    low_val = READ_32(GLOBAL_TIMER_COUNTER_LOW);
    high_val = READ_32(GLOBAL_TIMER_COUNTER_HIGH);

    if (low_val != test_low) {
        print2("[FAILED] Counter low write/read mismatch: read=0x%x, exp=0x%x\n",
               low_val, test_low);
        return;
    }

    if (high_val != test_high) {
        print2("[FAILED] Counter high write/read mismatch: read=0x%x, exp=0x%x\n",
               high_val, test_high);
        return;
    }

    print2("[INFO] Counter write/read verification passed\n");

    /* 设置定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, GTIMER_ENABLE_BIT);

    /* 短暂延时让计数器运行 */
    simple_delay(1000);

    /* 读取定时器高32位计数器寄存器和低32位计数器寄存器的值，确认其是在写入值基础上递增的 */
    uint32_t new_low = READ_32(GLOBAL_TIMER_COUNTER_LOW);
    uint32_t new_high = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
    uint64_t old_counter = ((uint64_t)test_high << 32) | test_low;
    uint64_t new_counter = ((uint64_t)new_high << 32) | new_low;

    if (new_counter <= old_counter) {
        print2("[FAILED] Counter not incrementing: old=0x%x%x, new=0x%x%x\n",
               test_high, test_low, new_high, new_low);
        return;
    }

    print2("[INFO] Counter incrementing verified: old=0x%x%x, new=0x%x%x\n",
           test_high, test_low, new_high, new_low);

    /* 停止定时器 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_3_2: Global timer counter test completed successfully\n");
}

void uc_kn_3_3(void)
{
    print2("[INFO] uc_kn_3_3: Starting global timer compare test\n");

    uint32_t cmp_val = 0x00010000;  /* 比较值 */
    uint64_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 10000;

    /* 清除全局定时器控制寄存器的定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    /* 清零全局定时器的计数器寄存器高32位和低32位 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0x00000000);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0x00000000);

    /* 设置比较值寄存器为cmp_val */
    WRITE_32(GLOBAL_TIMER_CMP_LOW, cmp_val);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, 0x00000000);

    /* 清除中断状态 */
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);

    /* 设置定时器使能位、比较使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, GTIMER_ENABLE_BIT | GTIMER_COMP_ENABLE_BIT);

    /* 不断读取全局定时器的计数器值和中断状态寄存器的事件标志位 */
    print2("[INFO] Monitoring counter and event flag...\n");

    while (timeout_count < max_timeout) {
        counter_val = global_timer_read();
        event_flag = READ_32(GLOBAL_TIMER_ISR) & GTIMER_EVENT_FLAG_BIT;

        if (counter_val < cmp_val) {
            /* 当计数器寄存器值小于比较值时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x%x) < compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }
        } else {
            /* 当计数器寄存器值大于等于比较值时，确认事件标志位为1 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter(0x%x%x) >= compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }

            print2("[INFO] Compare event triggered: counter=0x%x%x, compare=0x%x, flag=0x%x\n",
                   (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
            break;
        }

        timeout_count++;
        simple_delay(100);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach compare value\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_3_3: Global timer compare test completed successfully\n");
}

void uc_kn_3_4(void)
{
    print2("[INFO] uc_kn_3_4: Starting global timer auto-increment test\n");

    uint32_t cmp_val = 0x00008000;     /* 比较值 */
    uint32_t air_val = 0x00004000;     /* 自动递增值 */
    uint64_t counter_val;
    uint32_t compare_low, compare_high;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 15000;
    uint32_t phase = 0;  /* 0: 等待第一次比较, 1: 等待自动递增后的比较 */

    /* 清除全局定时器控制寄存器的定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    /* 清零全局定时器的计数器寄存器高32位和低32位 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0x00000000);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0x00000000);

    /* 设置比较值寄存器为cmp_val */
    WRITE_32(GLOBAL_TIMER_CMP_LOW, cmp_val);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, 0x00000000);

    /* 设置全局定时器的自动递增寄存器为air_val */
    WRITE_32(GLOBAL_TIMER_AUTO_INCR, air_val);

    /* 清除中断状态 */
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);

    /* 设置定时器使能位、比较使能位、自动递增使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, GTIMER_ENABLE_BIT | GTIMER_COMP_ENABLE_BIT | GTIMER_AUTO_INCR_BIT);

    /* 不断读取全局定时器的计数器值和比较值寄存器的值 */
    print2("[INFO] Monitoring counter and compare register values...\n");

    while (timeout_count < max_timeout) {
        counter_val = global_timer_read();
        compare_low = READ_32(GLOBAL_TIMER_CMP_LOW);
        compare_high = READ_32(GLOBAL_TIMER_CMP_HIGH);
        uint64_t compare_val = ((uint64_t)compare_high << 32) | compare_low;

        if (phase == 0) {
            /* 当计数器值小于cmp_val时，确认比较值寄存器的值为cmp_val */
            if (counter_val < cmp_val) {
                if (compare_low != cmp_val || compare_high != 0) {
                    print2("[FAILED] Phase 0: Compare register mismatch when counter < cmp_val: got=0x%x%x, exp=0x%x\n",
                           compare_high, compare_low, cmp_val);
                    return;
                }
            } else {
                /* 计数器达到比较值，进入下一阶段 */
                print2("[INFO] Counter reached compare value, checking auto-increment...\n");
                phase = 1;
            }
        } else {
            /* 当计数器值大于等于cmp_val时，确认比较值寄存器的值为cmp_val+air_val */
            uint32_t expected_cmp = cmp_val + air_val;
            if (counter_val >= cmp_val) {
                if (compare_low != expected_cmp || compare_high != 0) {
                    print2("[FAILED] Phase 1: Compare register should auto-increment: got=0x%x%x, exp=0x%x\n",
                           compare_high, compare_low, expected_cmp);
                    return;
                }

                print2("[INFO] Auto-increment verified: counter=0x%x%x, compare=0x%x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, compare_high, compare_low);
                break;
            }
        }

        timeout_count++;
        simple_delay(100);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for auto-increment behavior\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_3_4: Global timer auto-increment test completed successfully\n");
}

void uc_kn_3_5(void)
{
    print2("[INFO] uc_kn_3_5: Starting global timer event flag test\n");

    uint32_t cmp_val = 0x00020000;  /* 比较值 */
    uint64_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 12000;

    /* 清除全局定时器控制寄存器的定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    /* 清零全局定时器的计数器寄存器高32位和低32位 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0x00000000);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0x00000000);

    /* 设置比较值寄存器为cmp_val */
    WRITE_32(GLOBAL_TIMER_CMP_LOW, cmp_val);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, 0x00000000);

    /* 清除中断状态 */
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);

    /* 设置定时器使能位、比较使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, GTIMER_ENABLE_BIT | GTIMER_COMP_ENABLE_BIT);

    /* 不断读取全局定时器的计数器值和中断状态寄存器的事件标志位 */
    print2("[INFO] Monitoring counter and event flag (cmp_val=0x%x)...\n", cmp_val);

    while (timeout_count < max_timeout) {
        counter_val = global_timer_read();
        event_flag = READ_32(GLOBAL_TIMER_ISR) & GTIMER_EVENT_FLAG_BIT;

        if (counter_val < cmp_val) {
            /* 当计数器寄存器值小于比较值时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x%x) < compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }
        } else {
            /* 当计数器寄存器值大于比较值时，确认事件标志位为1 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter(0x%x%x) >= compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }

            print2("[INFO] Event flag correctly set: counter=0x%x%x, compare=0x%x, flag=0x%x\n",
                   (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach compare value\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_3_5: Global timer event flag test completed successfully\n");
}

void uc_kn_3_6(void)
{
    print2("[INFO] uc_kn_3_6: Starting global timer interrupt test\n");

    uint32_t cmp_val = 0x00030000;  /* 比较值 */
    uint64_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 15000;

    /* 清除全局定时器控制寄存器的定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    /* 清零全局定时器的计数器寄存器高32位和低32位 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0x00000000);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0x00000000);

    /* 设置比较值寄存器为cmp_val */
    WRITE_32(GLOBAL_TIMER_CMP_LOW, cmp_val);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, 0x00000000);

    /* 清除中断状态 */
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);

    /* 设置定时器使能位、比较使能位、中断使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, GTIMER_ENABLE_BIT | GTIMER_COMP_ENABLE_BIT | GTIMER_IRQ_ENABLE_BIT);

    print2("[INFO] Global timer started with interrupt enabled (cmp_val=0x%x)\n", cmp_val);
    print2("[INFO] Note: Check interrupt system for IRQ 27 (Global Timer) when event occurs\n");

    /* 不断读取全局定时器的计数器值和中断状态寄存器的事件标志位 */
    while (timeout_count < max_timeout) {
        counter_val = global_timer_read();
        event_flag = READ_32(GLOBAL_TIMER_ISR) & GTIMER_EVENT_FLAG_BIT;

        if (counter_val < cmp_val) {
            /* 当计数器寄存器值小于比较值时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x%x) < compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }
        } else {
            /* 当计数器寄存器值大于比较值时，确认事件标志位为1且中断系统触发相应中断 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter(0x%x%x) >= compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }

            print2("[INFO] Interrupt event triggered: counter=0x%x%x, compare=0x%x, flag=0x%x\n",
                   (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
            print2("[INFO] Global Timer IRQ 27 should be triggered in interrupt system\n");

            /* 清除中断状态以便观察 */
            WRITE_32(GLOBAL_TIMER_ISR, 0x1);
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach compare value\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_3_6: Global timer interrupt test completed successfully\n");
}
