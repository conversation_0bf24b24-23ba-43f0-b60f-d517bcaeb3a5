/*
 * 浮点及 SIMD 处理单元模块测试用例实现
 *
 * 测试用例范围：UC-KN-2-1 ~ UC-KN-2-2
 */

#include "uc-st-1.h"

#include "types.h"
#include "bsp_print.h"  /* 串口输出接口 */
#include <arm_neon.h>

void uc_kn_2_1(void)
{
    print2("[INFO] uc_kn_2_1: Starting test\n");

    /* 单精度浮点测试 */
    {
        float a, b, result, expected;

        /* 加法测试 */
        a = 3.14f;
        b = 2.86f;
        result = a + b;
        expected = 6.0f;
        if (result != expected) {
            print2("[FAILED] Single precision addition: %.6f + %.6f = %.6f, expected %.6f\n",
                   a, b, result, expected);
            return;
        }

        /* 减法测试 */
        a = 10.5f;
        b = 4.2f;
        result = a - b;
        expected = 6.3f;
        if (result != expected) {
            print2("[FAILED] Single precision subtraction: %.6f - %.6f = %.6f, expected %.6f\n",
                   a, b, result, expected);
            return;
        }

        /* 乘法测试 */
        a = 2.5f;
        b = 4.0f;
        result = a * b;
        expected = 10.0f;
        if (result != expected) {
            print2("[FAILED] Single precision multiplication: %.6f * %.6f = %.6f, expected %.6f\n",
                   a, b, result, expected);
            return;
        }

        /* 除法测试 */
        a = 15.0f;
        b = 3.0f;
        result = a / b;
        expected = 5.0f;
        if (result != expected) {
            print2("[FAILED] Single precision division: %.6f / %.6f = %.6f, expected %.6f\n",
                   a, b, result, expected);
            return;
        }

        /* 平方根测试 (简单实现) */
        a = 16.0f;
        result = a;
        /* 使用牛顿法计算平方根 */
        for (int i = 0; i < 10; i++) {
            result = 0.5f * (result + a / result);
        }
        expected = 4.0f;
        if (result < expected - 0.001f || result > expected + 0.001f) {
            print2("[FAILED] Single precision square root: sqrt(%.6f) = %.6f, expected %.6f\n",
                   a, result, expected);
            return;
        }

        print2("[INFO] Single precision tests passed\n");
    }

    /* 双精度浮点测试 */
    {
        double a, b, result, expected;

        /* 加法测试 */
        a = 3.141592653589793;
        b = 2.718281828459045;
        result = a + b;
        expected = 5.859874482048838;
        if (result != expected) {
            print2("[FAILED] Double precision addition: %.12f + %.12f = %.12f, expected %.12f\n",
                   a, b, result, expected);
            return;
        }

        /* 减法测试 */
        a = 10.123456789012345;
        b = 4.987654321098765;
        result = a - b;
        expected = 5.13580246791358;
        if (result != expected) {
            print2("[FAILED] Double precision subtraction: %.12f - %.12f = %.12f, expected %.12f\n",
                   a, b, result, expected);
            return;
        }

        /* 乘法测试 */
        a = 2.5;
        b = 4.0;
        result = a * b;
        expected = 10.0;
        if (result != expected) {
            print2("[FAILED] Double precision multiplication: %.12f * %.12f = %.12f, expected %.12f\n",
                   a, b, result, expected);
            return;
        }

        /* 除法测试 */
        a = 22.0;
        b = 7.0;
        result = a / b;
        expected = 3.142857142857143;
        if (result < expected - 1e-12 || result > expected + 1e-12) {
            print2("[FAILED] Double precision division: %.12f / %.12f = %.12f, expected %.12f\n",
                   a, b, result, expected);
            return;
        }

        /* 平方根测试 (简单实现) */
        a = 25.0;
        result = a;
        /* 使用牛顿法计算平方根 */
        for (int i = 0; i < 15; i++) {
            result = 0.5 * (result + a / result);
        }
        expected = 5.0;
        if (result < expected - 1e-12 || result > expected + 1e-12) {
            print2("[FAILED] Double precision square root: sqrt(%.12f) = %.12f, expected %.12f\n",
                   a, result, expected);
            return;
        }

        print2("[INFO] Double precision tests passed\n");
    }

    print2("[PASSED] uc_kn_2_1: All floating point operations test completed successfully\n");
}

void uc_kn_2_2(void)
{
    print2("[INFO] uc_kn_2_2: Starting NEON SIMD test\n");

    /* NEON 算术运算指令测试 */
    {
        /* 32位整数向量加法测试 */
        uint32x4_t vec_a = {1, 2, 3, 4};
        uint32x4_t vec_b = {5, 6, 7, 8};
        uint32x4_t result = vaddq_u32(vec_a, vec_b);
        uint32x4_t expected = {6, 8, 10, 12};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result, i) != vgetq_lane_u32(expected, i)) {
                print2("[FAILED] NEON u32 addition at lane %d: got %u, expected %u\n",
                       i, vgetq_lane_u32(result, i), vgetq_lane_u32(expected, i));
                return;
            }
        }

        /* 32位整数向量减法测试 */
        result = vsubq_u32(vec_b, vec_a);
        uint32x4_t expected_sub = {4, 4, 4, 4};
        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result, i) != vgetq_lane_u32(expected_sub, i)) {
                print2("[FAILED] NEON u32 subtraction at lane %d: got %u, expected %u\n",
                       i, vgetq_lane_u32(result, i), vgetq_lane_u32(expected_sub, i));
                return;
            }
        }

        /* 32位整数向量乘法测试 */
        uint32x4_t vec_c = {2, 3, 4, 5};
        uint32x4_t vec_d = {3, 2, 2, 2};
        result = vmulq_u32(vec_c, vec_d);
        uint32x4_t expected_mul = {6, 6, 8, 10};
        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result, i) != vgetq_lane_u32(expected_mul, i)) {
                print2("[FAILED] NEON u32 multiplication at lane %d: got %u, expected %u\n",
                       i, vgetq_lane_u32(result, i), vgetq_lane_u32(expected_mul, i));
                return;
            }
        }

        print2("[INFO] NEON arithmetic operations passed\n");
    }

    /* NEON 浮点运算测试 */
    {
        /* 单精度浮点向量加法 */
        float32x4_t vec_fa = {1.5f, 2.5f, 3.5f, 4.5f};
        float32x4_t vec_fb = {0.5f, 1.5f, 2.5f, 3.5f};
        float32x4_t result_f = vaddq_f32(vec_fa, vec_fb);
        float32x4_t expected_f = {2.0f, 4.0f, 6.0f, 8.0f};

        for (int i = 0; i < 4; i++) {
            float got = vgetq_lane_f32(result_f, i);
            float exp = vgetq_lane_f32(expected_f, i);
            if (got != exp) {
                print2("[FAILED] NEON f32 addition at lane %d: got %.6f, expected %.6f\n",
                       i, got, exp);
                return;
            }
        }

        /* 单精度浮点向量乘法 */
        float32x4_t vec_fc = {2.0f, 3.0f, 4.0f, 5.0f};
        float32x4_t vec_fd = {1.5f, 2.0f, 2.5f, 2.0f};
        result_f = vmulq_f32(vec_fc, vec_fd);
        float32x4_t expected_fmul = {3.0f, 6.0f, 10.0f, 10.0f};

        for (int i = 0; i < 4; i++) {
            float got = vgetq_lane_f32(result_f, i);
            float exp = vgetq_lane_f32(expected_fmul, i);
            if (got != exp) {
                print2("[FAILED] NEON f32 multiplication at lane %d: got %.6f, expected %.6f\n",
                       i, got, exp);
                return;
            }
        }

        print2("[INFO] NEON floating point operations passed\n");
    }

    /* NEON 内存访问指令测试 */
    {
        /* 准备测试数据 */
        uint32_t test_data[8] = {0x11111111, 0x22222222, 0x33333333, 0x44444444,
                                 0x55555555, 0x66666666, 0x77777777, 0x88888888};
        uint32_t result_data[4];

        /* 向量加载测试 (vld1q_u32) */
        uint32x4_t loaded_vec = vld1q_u32(test_data);

        /* 验证加载的数据 */
        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(loaded_vec, i) != test_data[i]) {
                print2("[FAILED] NEON load at lane %d: got 0x%x, expected 0x%x\n",
                       i, vgetq_lane_u32(loaded_vec, i), test_data[i]);
                return;
            }
        }

        /* 向量存储测试 (vst1q_u32) */
        uint32x4_t store_vec = {0xAAAAAAAA, 0xBBBBBBBB, 0xCCCCCCCC, 0xDDDDDDDD};
        vst1q_u32(result_data, store_vec);

        /* 验证存储的数据 */
        uint32_t expected_store[4] = {0xAAAAAAAA, 0xBBBBBBBB, 0xCCCCCCCC, 0xDDDDDDDD};
        for (int i = 0; i < 4; i++) {
            if (result_data[i] != expected_store[i]) {
                print2("[FAILED] NEON store at index %d: got 0x%x, expected 0x%x\n",
                       i, result_data[i], expected_store[i]);
                return;
            }
        }

        print2("[INFO] NEON memory access operations passed\n");
    }

    /* NEON 逻辑与位操作指令测试 */
    {
        /* 按位与操作测试 */
        uint32x4_t vec_and_a = {0xF0F0F0F0, 0xFF00FF00, 0xAAAAAAAA, 0x12345678};
        uint32x4_t vec_and_b = {0x0F0F0F0F, 0x00FF00FF, 0x55555555, 0x87654321};
        uint32x4_t result_and = vandq_u32(vec_and_a, vec_and_b);
        uint32x4_t expected_and = {0x00000000, 0x00000000, 0x00000000, 0x02244220};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result_and, i) != vgetq_lane_u32(expected_and, i)) {
                print2("[FAILED] NEON AND at lane %d: got 0x%x, expected 0x%x\n",
                       i, vgetq_lane_u32(result_and, i), vgetq_lane_u32(expected_and, i));
                return;
            }
        }

        /* 按位或操作测试 */
        uint32x4_t result_or = vorrq_u32(vec_and_a, vec_and_b);
        uint32x4_t expected_or = {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x97755779};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result_or, i) != vgetq_lane_u32(expected_or, i)) {
                print2("[FAILED] NEON OR at lane %d: got 0x%x, expected 0x%x\n",
                       i, vgetq_lane_u32(result_or, i), vgetq_lane_u32(expected_or, i));
                return;
            }
        }

        /* 按位异或操作测试 */
        uint32x4_t result_eor = veorq_u32(vec_and_a, vec_and_b);
        uint32x4_t expected_eor = {0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x95511559};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result_eor, i) != vgetq_lane_u32(expected_eor, i)) {
                print2("[FAILED] NEON EOR at lane %d: got 0x%x, expected 0x%x\n",
                       i, vgetq_lane_u32(result_eor, i), vgetq_lane_u32(expected_eor, i));
                return;
            }
        }

        /* 按位取反操作测试 */
        uint32x4_t vec_not = {0x00000000, 0xFFFFFFFF, 0xAAAAAAAA, 0x55555555};
        uint32x4_t result_not = vmvnq_u32(vec_not);
        uint32x4_t expected_not = {0xFFFFFFFF, 0x00000000, 0x55555555, 0xAAAAAAAA};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result_not, i) != vgetq_lane_u32(expected_not, i)) {
                print2("[FAILED] NEON NOT at lane %d: got 0x%x, expected 0x%x\n",
                       i, vgetq_lane_u32(result_not, i), vgetq_lane_u32(expected_not, i));
                return;
            }
        }

        print2("[INFO] NEON logical operations passed\n");
    }

    /* NEON 位移操作测试 */
    {
        /* 左移测试 */
        uint32x4_t vec_shift = {1, 2, 4, 8};
        uint32x4_t result_shl = vshlq_n_u32(vec_shift, 2);  /* 左移2位 */
        uint32x4_t expected_shl = {4, 8, 16, 32};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result_shl, i) != vgetq_lane_u32(expected_shl, i)) {
                print2("[FAILED] NEON left shift at lane %d: got %u, expected %u\n",
                       i, vgetq_lane_u32(result_shl, i), vgetq_lane_u32(expected_shl, i));
                return;
            }
        }

        /* 右移测试 */
        uint32x4_t vec_shift_r = {32, 64, 128, 256};
        uint32x4_t result_shr = vshrq_n_u32(vec_shift_r, 3);  /* 右移3位 */
        uint32x4_t expected_shr = {4, 8, 16, 32};

        for (int i = 0; i < 4; i++) {
            if (vgetq_lane_u32(result_shr, i) != vgetq_lane_u32(expected_shr, i)) {
                print2("[FAILED] NEON right shift at lane %d: got %u, expected %u\n",
                       i, vgetq_lane_u32(result_shr, i), vgetq_lane_u32(expected_shr, i));
                return;
            }
        }

        print2("[INFO] NEON shift operations passed\n");
    }

    print2("[PASSED] uc_kn_2_2: All NEON SIMD operations test completed successfully\n");
}
